# Docker Compose for local development and testing
version: '3.8'

services:
  algorithm-visualizer:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
    volumes:
      - ./:/app:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for local development
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - algorithm-visualizer
    restart: unless-stopped
    profiles:
      - proxy

networks:
  default:
    name: algorithm-visualizer-network
