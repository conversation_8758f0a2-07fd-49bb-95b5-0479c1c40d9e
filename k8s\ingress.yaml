apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: algorithm-visualizer-ingress
  namespace: algorithm-visualizer
  labels:
    app: algorithm-visualizer
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '5'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '3'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    # Uncomment and configure for HTTPS
    # alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:region:account:certificate/certificate-id
    # alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
spec:
  rules:
  - host: algorithm-visualizer.yourdomain.com  # Replace with your domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: algorithm-visualizer-service
            port:
              number: 80
  # Uncomment for HTTPS
  # tls:
  # - hosts:
  #   - algorithm-visualizer.yourdomain.com
  #   secretName: algorithm-visualizer-tls
