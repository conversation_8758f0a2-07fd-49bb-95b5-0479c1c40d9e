# Environment Configuration Template
# Copy this file to .env and update the values

# Docker Configuration
DOCKER_HUB_USERNAME=your-dockerhub-username
DOCKER_IMAGE_NAME=algorithm-visualizer

# AWS Configuration
AWS_REGION=us-west-2
AWS_ACCOUNT_ID=************

# EKS Configuration
EKS_CLUSTER_NAME=algorithm-visualizer-cluster
EKS_NODE_GROUP_NAME=algorithm-visualizer-nodes

# Jenkins Configuration
JENKINS_URL=http://your-jenkins-server:8080
JENKINS_USER=admin

# Application Configuration
APP_NAME=algorithm-visualizer
APP_NAMESPACE=algorithm-visualizer
APP_DOMAIN=algorithm-visualizer.yourdomain.com

# SSL Certificate (for HTTPS)
SSL_CERTIFICATE_ARN=arn:aws:acm:region:account:certificate/certificate-id

# Monitoring (optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Database (if needed in future)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=algorithm_visualizer
# DB_USER=app_user
# DB_PASSWORD=secure_password
